# QWEN.md

This file provides guidance to Qwen Code when working with code in this repository.

## Project Overview

SimplePOS Hybrid is a smart Point of Sale system that combines conversational AI with traditional interface elements. The system intelligently adapts between visual product buttons and natural language input based on user behavior and preference.

## Architecture

### Single-File Application
- **index.html**: Complete single-page application with embedded CSS and JavaScript
- **No build process**: Direct HTML/CSS/JavaScript implementation
- **No package.json**: Uses CDN-hosted dependencies (TailwindCSS, FontAwesome, Chart.js)

### Core Components
- **Hybrid Interface System**: Adapts between conversation-only, traditional buttons, and smart hybrid modes
- **Natural Language Processing**: Client-side parsing for product recognition and order processing
- **Adaptive User Profiling**: Tracks user preferences and skill level progression
- **Order Management**: Full cart functionality with real-time calculations

### Key Technologies
- **Frontend**: Vanilla JavaScript, TailwindCSS for styling, FontAwesome for icons
- **Storage**: localStorage for user preferences persistence
- **No backend**: Fully client-side application

## Development Commands

Since this is a single HTML file with no build process:

### Development
```bash
# Serve the file locally (any method)
python -m http.server 8000
# OR
npx serve .
# OR open index.html directly in browser
```

### No Linting/Testing
- No configured linting tools
- No test framework
- No package.json or build scripts

## Core System Architecture

### State Management
```javascript
// Application state stored in global variables
currentOrder = []           // Current shopping cart
userPreferences = {}        // User behavior tracking
chatHistory = []            // Conversation log
```

### Adaptive Intelligence System
The system learns user behavior through:
- **Conversation vs Button Usage Tracking**: Adjusts interface prominence
- **Skill Level Detection**: 'learning' → 'intermediate' → 'expert' → 'traditional'
- **Interface Mode Switching**: Smart Hybrid → Conversation Only → Traditional Only

### Natural Language Processing
Client-side text parsing handles:
- Quantity extraction (numbers and word numbers)
- Product matching with variations
- Command recognition (checkout, discount, clear)
- Order modification (add, remove items)

### Product System
Hardcoded product catalog with:
- Product variations for NLP matching
- Fixed pricing structure
- Tax calculation (8.5%)

## Key Functions and Architecture

### Message Processing Flow
```
User Input → processNaturalLanguage() → Product Matching → Order Update → UI Refresh
```

### Interface Adaptation Logic
```
User Behavior → updateUserLevel() → Interface Adjustment → Preference Storage
```

### Order Management
```
addToOrder() → updateOrderDisplay() → Calculate Totals → Enable Checkout
```

## Important Implementation Details

### User Experience Modes
- **Smart Hybrid**: Default mode combining conversation and visual elements
- **Conversation Only**: Pure text-based interaction
- **Traditional Only**: Button-only interface

### Adaptive Features
- Product grid visibility adjusts based on user skill level
- Help text changes based on user proficiency
- Interface complexity scales with user expertise

### State Persistence
User preferences saved to localStorage including:
- Preferred interaction mode
- Usage statistics (conversation vs button usage)
- Skill level progression

## Working with This Codebase

### Making Changes
- Edit index.html directly
- All styling in `<style>` tag
- All JavaScript in `<script>` tag at bottom
- Refresh browser to see changes

### Testing
- Manual testing in browser only
- Test different user interaction patterns to verify adaptive behavior
- Clear localStorage to reset user preferences for testing

### Adding Features
- Extend the `products` array for new items
- Modify `processNaturalLanguage()` for new command patterns
- Update adaptive logic in `updateUserLevel()` for new behaviors